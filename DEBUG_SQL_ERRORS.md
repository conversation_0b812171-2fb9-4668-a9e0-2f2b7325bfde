# SQL 错误调试指南

## 问题分析

你遇到的错误是由于 TypeORM 查询构建器中的 SQL 语法错误导致的。具体问题是在 `IN` 子句中缺少右括号 `)`。

### 已修复的错误

1. **src/modules/rbac/rbac.resolver.ts:246**
   ```typescript
   // 错误的写法
   .where('roles.id IN (:...ids', { ids: [superRole.id] })
   
   // 正确的写法
   .where('roles.id IN (:...ids)', { ids: [superRole.id] })
   ```

2. **src/modules/user/services/user.service.ts:135,138**
   ```typescript
   // 错误的写法
   qb.andWhere('roles.id IN (:...roles', { roles: [options.role] });
   qb.andWhere('permissions.id IN (:...permissions', { permissions: [options.permission] });
   
   // 正确的写法
   qb.andWhere('roles.id IN (:...roles)', { roles: [options.role] });
   qb.andWhere('permissions.id IN (:...permissions)', { permissions: [options.permission] });
   ```

3. **src/modules/rbac/services/permission.service.ts:37**
   ```typescript
   // 错误的写法
   qb.andWhere('role.id IN (:...roles', { roles: [options.role] });
   
   // 正确的写法
   qb.andWhere('role.id IN (:...roles)', { roles: [options.role] });
   ```

## 增强的错误日志配置

### 1. 数据库日志配置

已更新 `src/config/database.config.ts` 以启用详细的 SQL 日志：

```typescript
export const database = createDBConfig((configure) => ({
    common: { 
        synchronize: true,
        // 开发环境启用详细日志
        logging: configure.env.get('NODE_ENV') === 'development' 
            ? ['query', 'error', 'schema', 'warn', 'info', 'log'] 
            : ['error'],
        // 记录慢查询
        maxQueryExecutionTime: 1000,
    },
    // ...
}));
```

### 2. 全局异常过滤器

创建了 `src/modules/core/filters/global-exception.filter.ts` 来捕获和记录完整的错误信息：

- 专门处理 TypeORM 的 `QueryFailedError`
- 记录完整的 SQL 查询、参数和错误堆栈
- 提供结构化的错误响应

### 3. 环境变量配置

在你的 `.env` 文件中添加以下配置来启用详细日志：

```env
NODE_ENV=development
DB_HOST=*************
DB_PORT=3306
DB_USERNAME=3r
DB_PASSWORD=12345678
DB_NAME=3r
```

## 如何调试 SQL 错误

### 1. 启用详细日志

确保在开发环境中设置 `NODE_ENV=development`，这将启用所有 SQL 查询的日志记录。

### 2. 查看控制台输出

启动应用后，你将看到：
- 所有执行的 SQL 查询
- 查询参数
- 执行时间
- 详细的错误信息

### 3. 错误信息结构

现在的错误响应包含：
```json
{
  "statusCode": 400,
  "timestamp": "2025-07-01T...",
  "path": "/api/...",
  "method": "GET",
  "message": "Database query failed",
  "details": {
    "query": "SELECT ... WHERE roles.id IN (?)",
    "parameters": ["61035bbd-86c5-4031-b837-3c6816f85532"],
    "driverError": "You have an error in your SQL syntax...",
    "sqlMessage": "...",
    "code": "ER_PARSE_ERROR",
    "errno": 1064
  }
}
```

## 预防类似错误

### 1. 代码审查检查清单

- 检查所有 `IN (...)` 子句是否有匹配的括号
- 验证 TypeORM 查询构建器的语法
- 使用 TypeScript 的严格模式

### 2. 单元测试

为查询构建器编写单元测试：

```typescript
describe('UserService', () => {
  it('should build correct query with role filter', async () => {
    const qb = userRepository.createQueryBuilder('user');
    const result = await userService.buildListQB(qb, { role: 'test-id' });
    
    // 验证生成的 SQL 语法正确
    expect(result.getSql()).toContain('roles.id IN (?)');
  });
});
```

### 3. 使用 ESLint 规则

可以添加自定义 ESLint 规则来检测常见的 SQL 语法错误。

## 测试修复

运行测试脚本验证修复：

```bash
npx ts-node src/test-sql-fix.ts
```

## 重启应用

修复完成后，重启你的应用：

```bash
npm run start:dev
# 或
yarn start:dev
```

现在应用应该能够正常启动，不再出现 SQL 语法错误。
