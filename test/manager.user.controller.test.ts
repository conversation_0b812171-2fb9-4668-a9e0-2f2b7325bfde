import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';
import { UserRepository } from '@/modules/user/repositories';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/app/user/manager';

describe('Manager UserController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let userRepository: UserRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        userRepository = app.get<UserRepository>(UserRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(userRepository).toBeDefined();
    });

    describe('GET /users - 用户列表', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?limit=0`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /users/:id - 获取用户信息', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('POST /users - 新增用户', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with missing username without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with missing password without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'testuser',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with password mismatch without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'differentpassword',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with short username without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'ab',
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long username without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'A'.repeat(31),
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid email without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'password123',
                    email: 'invalid-email',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid phone without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'password123',
                    phone: 'invalid-phone',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid roles without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/users`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'password123',
                    roles: ['invalid-role-id'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /users - 更新用户', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/users`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    username: 'updateduser',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/users`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid id format without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/users`,
                body: {
                    id: 'invalid-uuid',
                    username: 'updateduser',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /users - 批量删除用户', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/users`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/users`,
                body: {
                    ids: [],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/users`,
                body: {
                    ids: ['invalid-uuid'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /users/restore - 批量恢复用户', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/users/restore`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/users/restore`,
                body: {
                    ids: [],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/users/restore`,
                body: {
                    ids: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
