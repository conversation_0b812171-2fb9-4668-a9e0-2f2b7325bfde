import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { CommentRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/content';


describe('CommentController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let commentRepository: CommentRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        commentRepository = app.get<CommentRepository>(CommentRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(commentRepository).toBeDefined();
    });

    describe('GET /comment/tree - 查询评论树', () => {
        it('should return comment tree with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment/tree`,
            });
            expect(result.statusCode).toEqual(200);
            expect(Array.isArray(result.json())).toBe(true);
        });

        it('should return comment tree with post filter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment/tree?post=74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(200);
            expect(Array.isArray(result.json())).toBe(true);
        });

        it('should handle invalid post UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment/tree?post=invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle non-existent post id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment/tree?post=74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(200);
            expect(Array.isArray(result.json())).toBe(true);
        });
    });

    describe('GET /comment - 查询评论列表', () => {
        it('should return paginated comments with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response).toHaveProperty('items');
            expect(response).toHaveProperty('meta');
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should return paginated comments with custom page and limit', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?page=1&limit=5`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.meta.itemsPerPage).toBe(5);
        });

        it('should filter comments by post', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?post=74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should fail with invalid page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?page=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?limit=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle invalid post UUID format in query', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?post=invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle orderBy parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?orderBy=createdAt`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle order parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comment?order=DESC`,
            });
            expect(result.statusCode).toEqual(200);
        });
    });

    describe('POST /comment - 新增评论', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/comment`,
                body: {
                    body: 'Test comment',
                    post: '74e655b3-b69a-42ae-a101-41c224386e74',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/comment`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid post UUID without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/comment`,
                body: {
                    body: 'Test comment',
                    post: 'invalid-uuid',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid parent UUID without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/comment`,
                body: {
                    body: 'Test comment',
                    post: '74e655b3-b69a-42ae-a101-41c224386e74',
                    parent: 'invalid-uuid',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /comment - 批量删除评论', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comment`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comment`,
                body: {
                    ids: [],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comment`,
                body: {
                    ids: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail without ids field without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comment`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
