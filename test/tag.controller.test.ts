import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { TagRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/content';

describe('TagController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let tagRepository: TagRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        tagRepository = app.get<TagRepository>(TagRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(tagRepository).toBeDefined();
    });

    describe('GET /tag - 分页查询标签列表', () => {
        it('should return paginated tags with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response).toHaveProperty('items');
            expect(response).toHaveProperty('meta');
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should return paginated tags with custom page and limit', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=1&limit=5`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.meta.itemsPerPage).toBe(5);
        });

        it('should fail with invalid page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=0`,
            });
            expect(result.statusCode).toEqual(400);
            expect(result.json()).toEqual({
                message: ['The current page must be greater than 1.'],
                error: 'Bad Request',
                statusCode: 400,
            });
        });

        it('should fail with invalid limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?limit=0`,
            });
            expect(result.statusCode).toEqual(400);
            expect(result.json()).toEqual({
                message: ['The number of data displayed per page must be greater than 1.'],
                error: 'Bad Request',
                statusCode: 400,
            });
        });

        it('should handle negative page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=-1`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle negative limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?limit=-5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle very large page number', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=999999`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.items).toEqual([]);
        });

        it('should handle very large limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?limit=1000`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle string page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=abc`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle string limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?limit=xyz`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=1.5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?limit=5.5`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    describe('GET /tag/:id - 查询标签详情', () => {
        let testTagId: string;

        beforeAll(async () => {
            // 创建测试标签
            const createResult = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {
                    name: 'Test Tag for Detail',
                    desc: 'Test description',
                },
            });
            if (createResult.statusCode === 201) {
                testTagId = createResult.json().id;
            }
        });

        afterAll(async () => {
            // 清理测试数据
            if (testTagId) {
                await app.inject({
                    method: 'DELETE',
                    url: `${URL_PREFIX}/tag`,
                    body: { ids: [testTagId] },
                });
            }
        });

        it('should return tag detail with valid id', async () => {
            if (!testTagId) {
                // 如果没有测试ID，跳过此测试
                return;
            }
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/${testTagId}`,
            });
            expect(result.statusCode).toEqual(200);
            const tag = result.json();
            expect(tag).toHaveProperty('id');
            expect(tag).toHaveProperty('name');
            expect(tag.id).toBe(testTagId);
        });

        it('should fail with invalid UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with non-existent tag id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with empty id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with malformed UUID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/not-a-valid-uuid-123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with numeric id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with special characters in id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/@#$%^&*()`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
