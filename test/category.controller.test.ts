import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { CategoryRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/content';

describe('CategoryController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let categoryRepository: CategoryRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        categoryRepository = app.get<CategoryRepository>(CategoryRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(categoryRepository).toBeDefined();
    });

    describe('GET /category/tree - 查询分类树', () => {
        it('should return category tree successfully', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/tree`,
            });
            expect(result.statusCode).toEqual(200);
            expect(Array.isArray(result.json())).toBe(true);
        });
    });

    describe('GET /category - 分页查询分类列表', () => {
        it('should return paginated categories with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response).toHaveProperty('items');
            expect(response).toHaveProperty('meta');
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should return paginated categories with custom page and limit', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=1&limit=5`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.meta.itemsPerPage).toBe(5);
        });

        it('should fail with invalid page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=0`,
            });
            expect(result.statusCode).toEqual(400);
            expect(result.json()).toEqual({
                message: ['The current page must be greater than 1.'],
                error: 'Bad Request',
                statusCode: 400,
            });
        });

        it('should fail with invalid limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?limit=0`,
            });
            expect(result.statusCode).toEqual(400);
            expect(result.json()).toEqual({
                message: ['The number of data displayed per page must be greater than 1.'],
                error: 'Bad Request',
                statusCode: 400,
            });
        });

        it('should handle negative page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=-1`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle negative limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?limit=-5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle very large page number', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=999999`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.items).toEqual([]);
        });

        it('should handle very large limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?limit=1000`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle string page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=abc`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle string limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?limit=xyz`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=1.5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?limit=5.5`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    describe('GET /category/:id - 查询分类详情', () => {
        let testCategoryId: string;

        beforeAll(async () => {
            // 创建测试分类
            const createResult = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/category`,
                body: {
                    name: 'Test Category for Detail',
                },
            });
            if (createResult.statusCode === 201) {
                testCategoryId = createResult.json().id;
            }
        });

        afterAll(async () => {
            // 清理测试数据
            if (testCategoryId) {
                await app.inject({
                    method: 'DELETE',
                    url: `${URL_PREFIX}/category/${testCategoryId}`,
                });
            }
        });

        it('should return category detail with valid id', async () => {
            if (!testCategoryId) {
                // 如果没有测试ID，跳过此测试
                return;
            }
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/${testCategoryId}`,
            });
            expect(result.statusCode).toEqual(200);
            const category = result.json();
            expect(category).toHaveProperty('id');
            expect(category).toHaveProperty('name');
            expect(category.id).toBe(testCategoryId);
        });

        it('should fail with invalid UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with non-existent category id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with empty id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with malformed UUID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/not-a-valid-uuid-123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with numeric id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with special characters in id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/@#$%^&*()`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
