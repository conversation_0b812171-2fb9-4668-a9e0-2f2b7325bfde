import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { PostRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manage/content';


describe('Manager PostController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let postRepository: PostRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        postRepository = app.get<PostRepository>(PostRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(postRepository).toBeDefined();
    });

    describe('GET /posts - 查询文章列表', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?page=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?limit=0`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /posts/:id - 查询文章详情', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('POST /posts - 新增文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long title without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'A'.repeat(256),
                    body: 'Test content',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long summary without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                    summary: 'A'.repeat(501),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid category UUID without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                    category: 'invalid-uuid',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid tag UUIDs without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                    tags: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long keywords without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                    keywords: ['A'.repeat(21)],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative customOrder without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                    customOrder: -1,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid author UUID without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                    author: 'invalid-uuid',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /posts - 更新文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    title: 'Updated Post',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid id format without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts`,
                body: {
                    id: 'invalid-uuid',
                    title: 'Updated Post',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long title without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    title: 'A'.repeat(256),
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /posts - 批量删除文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/posts`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/posts`,
                body: {
                    ids: [],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/posts`,
                body: {
                    ids: ['invalid-uuid'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /posts/restore - 批量恢复文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts/restore`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts/restore`,
                body: {
                    ids: [],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts/restore`,
                body: {
                    ids: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
