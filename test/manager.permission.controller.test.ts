import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';
import { PermissionRepository } from '@/modules/rbac/repositories';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manage/rbac';

describe('Manager PermissionController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let permissionRepository: PermissionRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        permissionRepository = app.get<PermissionRepository>(PermissionRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(permissionRepository).toBeDefined();
    });

    describe('GET /permissions - 分页列表查询', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?limit=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=-1`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?limit=-5`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with very large page number without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=999999`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with very large limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?limit=1000`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with string page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=abc`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with string limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?limit=xyz`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with float page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=1.5`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with float limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?limit=5.5`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with trashed parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?trashed=all`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with trashed parameter with only value without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?trashed=only`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with trashed parameter with none value without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?trashed=none`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid trashed parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?trashed=invalid`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with orderBy parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?orderBy=createdAt`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with order parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?order=DESC`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /permissions/:id - 权限详情查询', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty id without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with malformed UUID without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/not-a-valid-uuid-123`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with numeric id without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/123`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with special characters in id without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/@#$%^&*()`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with non-existent permission id without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
