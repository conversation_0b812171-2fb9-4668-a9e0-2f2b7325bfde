import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';
import { UserRepository } from '@/modules/user/repositories';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/user';

describe('UserController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let userRepository: UserRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        userRepository = app.get<UserRepository>(UserRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(userRepository).toBeDefined();
    });

    describe('GET /users - 用户列表', () => {
        it('should return paginated users with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response).toHaveProperty('items');
            expect(response).toHaveProperty('meta');
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should return paginated users with custom page and limit', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=1&limit=5`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.meta.itemsPerPage).toBe(5);
        });

        it('should fail with invalid page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?limit=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle negative page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=-1`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle negative limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?limit=-5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle very large page number', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=999999`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.items).toEqual([]);
        });

        it('should handle very large limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?limit=1000`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle string page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=abc`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle string limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?limit=xyz`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?page=1.5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?limit=5.5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle search parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?search=test`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle orderBy parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?orderBy=createdAt`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle order parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users?order=DESC`,
            });
            expect(result.statusCode).toEqual(200);
        });
    });

    describe('GET /users/:id - 获取用户信息', () => {
        it('should fail with invalid UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with non-existent user id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with empty id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with malformed UUID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/not-a-valid-uuid-123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with numeric id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with special characters in id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/users/@#$%^&*()`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
