import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';
import { UserRepository } from '@/modules/user/repositories';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/user';

describe('AccountController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let userRepository: UserRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        userRepository = app.get<UserRepository>(UserRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(userRepository).toBeDefined();
    });

    describe('POST /account/register - 用户注册', () => {
        it('should fail with empty body', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {},
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with missing username', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with missing password', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with missing plainPassword', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with password mismatch', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'differentpassword',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with short password', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    password: '123',
                    plainPassword: '123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with long password', async () => {
            const longPassword = 'A'.repeat(129);
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    password: longPassword,
                    plainPassword: longPassword,
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with short username', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'ab',
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with long username', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'A'.repeat(31),
                    password: 'password123',
                    plainPassword: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid email format', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'password123',
                    email: 'invalid-email',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid phone format', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/register`,
                body: {
                    username: 'testuser',
                    password: 'password123',
                    plainPassword: 'password123',
                    phone: 'invalid-phone',
                },
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    describe('POST /account/login - 用户登录', () => {
        it('should fail with empty body', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/login`,
                body: {},
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with missing credential', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/login`,
                body: {
                    password: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with missing password', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/login`,
                body: {
                    credential: 'testuser',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid credentials', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/login`,
                body: {
                    credential: 'nonexistentuser',
                    password: 'wrongpassword',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty credential', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/login`,
                body: {
                    credential: '',
                    password: 'password123',
                },
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with empty password', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/login`,
                body: {
                    credential: 'testuser',
                    password: '',
                },
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    describe('POST /account/logout - 注销登录', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/account/logout`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /account/profile - 获取账户信息', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/account/profile`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /account - 更改账户信息', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/account`,
                body: {
                    username: 'newusername',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid data without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/account`,
                body: {
                    username: 'ab', // too short
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /account/change-password - 修改密码', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/account/change-password`,
                body: {
                    oldPassword: 'oldpassword',
                    password: 'newpassword',
                    plainPassword: 'newpassword',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with missing fields without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/account/change-password`,
                body: {
                    password: 'newpassword',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with password mismatch without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/account/change-password`,
                body: {
                    oldPassword: 'oldpassword',
                    password: 'newpassword',
                    plainPassword: 'differentpassword',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
