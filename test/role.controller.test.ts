import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';
import { RoleRepository } from '@/modules/rbac/repositories';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/rbac';

describe('RoleController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let roleRepository: RoleRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        roleRepository = app.get<RoleRepository>(RoleRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(roleRepository).toBeDefined();
    });

    describe('GET /roles - 角色列表查询', () => {
        it('should return paginated roles with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response).toHaveProperty('items');
            expect(response).toHaveProperty('meta');
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should return paginated roles with custom page and limit', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?page=1&limit=5`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.meta.itemsPerPage).toBe(5);
        });

        it('should fail with invalid page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?page=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?limit=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle negative page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?page=-1`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle negative limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?limit=-5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle very large page number', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?page=999999`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.items).toEqual([]);
        });

        it('should handle very large limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?limit=1000`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle string page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?page=abc`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle string limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?limit=xyz`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?page=1.5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle float limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?limit=5.5`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle trashed parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?trashed=all`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle trashed parameter with only value', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?trashed=only`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle trashed parameter with none value', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?trashed=none`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle invalid trashed parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?trashed=invalid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle orderBy parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?orderBy=createdAt`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle order parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles?order=DESC`,
            });
            expect(result.statusCode).toEqual(200);
        });
    });

    describe('GET /roles/:id - 角色详解查询', () => {
        it('should fail with invalid UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with non-existent role id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with empty id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with malformed UUID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles/not-a-valid-uuid-123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with numeric id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles/123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with special characters in id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/roles/@#$%^&*()`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
