import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { CommentRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manage/content';

describe('Manager CommentController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let commentRepository: CommentRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        commentRepository = app.get<CommentRepository>(CommentRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(commentRepository).toBeDefined();
    });

    describe('GET /comments - 查询评论列表', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?page=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?limit=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with post filter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?post=74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid post UUID without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?post=invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with orderBy parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?orderBy=createdAt`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with order parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?order=DESC`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?page=-1`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?limit=-5`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with very large page number without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?page=999999`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with very large limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?limit=1000`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with string page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?page=abc`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with string limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?limit=xyz`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with float page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?page=1.5`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with float limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/comments?limit=5.5`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /comments - 批量删除评论', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: [],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail without ids field without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with null ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: null,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with string ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: 'not-an-array',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with mixed valid and invalid UUIDs without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74', 'invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with numeric ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: [123, 456],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with special characters in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: ['@#$%^&*()'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with very long ids array without authentication', async () => {
            const longIds = Array(1000).fill('74e655b3-b69a-42ae-a101-41c224386e74');
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/comments`,
                body: {
                    ids: longIds,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
