import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';
import { RoleRepository } from '@/modules/rbac/repositories';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manage/rbac';

describe('Manager RoleController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let roleRepository: RoleRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        roleRepository = app.get<RoleRepository>(RoleRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(roleRepository).toBeDefined();
    });

    describe('POST /roles - 新增角色', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                    label: 'test-role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with missing name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    label: 'test-role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with missing label without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: '',
                    label: 'test-role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty label without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                    label: '',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'A'.repeat(256),
                    label: 'test-role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long label without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                    label: 'A'.repeat(256),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long description without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                    label: 'test-role',
                    description: 'A'.repeat(501),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid permissions without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                    label: 'test-role',
                    permissions: ['invalid-permission-id'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative customOrder without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Test Role',
                    label: 'test-role',
                    customOrder: -1,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /roles - 更新角色', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'Updated Role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid id format without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {
                    id: 'invalid-uuid',
                    name: 'Updated Role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with missing id without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {
                    name: 'Updated Role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long name without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'A'.repeat(256),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long label without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    label: 'A'.repeat(256),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with non-existent id without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'Updated Role',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /roles - 批量删除角色', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/roles`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/roles`,
                body: {
                    ids: [],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/roles`,
                body: {
                    ids: ['invalid-uuid'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail without ids field without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/roles`,
                body: {
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /roles/restore - 批量恢复角色', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles/restore`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles/restore`,
                body: {
                    ids: [],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles/restore`,
                body: {
                    ids: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail without ids field without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/roles/restore`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
