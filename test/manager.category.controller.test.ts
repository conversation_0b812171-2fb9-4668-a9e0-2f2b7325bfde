import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { CategoryRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manage/content';

describe('Manager CategoryController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let categoryRepository: CategoryRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        categoryRepository = app.get<CategoryRepository>(CategoryRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(categoryRepository).toBeDefined();
    });

    describe('GET /category/tree - 查询分类树', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/tree`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /category - 分页查询分类列表', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?page=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category?limit=0`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /category/:id - 查询分类详情', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/category/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('POST /category - 创建分类', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/category`,
                body: {
                    name: 'Test Category',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/category`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid parent UUID without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/category`,
                body: {
                    name: 'Test Category',
                    parent: 'invalid-uuid',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative customOrder without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/category`,
                body: {
                    name: 'Test Category',
                    customOrder: -1,
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/category`,
                body: {
                    name: 'A'.repeat(30),
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /category - 更新分类', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/category`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'Updated Category',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/category`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid id format without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/category`,
                body: {
                    id: 'invalid-uuid',
                    name: 'Updated Category',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long name without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/category`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'A'.repeat(30),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid parent UUID without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/category`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    parent: 'invalid-uuid',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with negative customOrder without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/category`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    customOrder: -1,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /category/:id - 删除分类', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/category/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/category/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty id without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/category/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with non-existent id without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/category/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
