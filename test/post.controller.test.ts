import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { PostRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/content';

describe('PostController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let postRepository: PostRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        postRepository = app.get<PostRepository>(PostRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(postRepository).toBeDefined();
    });

    describe('GET /posts - 查询文章列表', () => {
        it('should return paginated posts with default params', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response).toHaveProperty('items');
            expect(response).toHaveProperty('meta');
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should return paginated posts with custom page and limit', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?page=1&limit=5`,
            });
            expect(result.statusCode).toEqual(200);
            const response = result.json();
            expect(response.meta.itemsPerPage).toBe(5);
        });

        it('should filter posts by category', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?category=test-category-id`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should filter posts by tag', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?tag=test-tag-id`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should search posts by keyword', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?search=test`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should fail with invalid page parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?page=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with invalid limit parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?limit=0`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should handle orderBy parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?orderBy=createdAt`,
            });
            expect(result.statusCode).toEqual(200);
        });

        it('should handle order parameter', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts?order=DESC`,
            });
            expect(result.statusCode).toEqual(200);
        });
    });

    describe('GET /posts/owner - 查询自己发布的文章列表', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/owner`,
            });
            expect(result.statusCode).toEqual(401);
        });

        // Note: 需要认证的测试需要在有认证token的情况下进行
        // 这里只测试未认证的情况
    });

    describe('GET /posts/:id - 查询文章详情', () => {
        it('should fail with invalid UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with non-existent post id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with empty id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/`,
            });
            expect(result.statusCode).toEqual(404);
        });

        it('should fail with malformed UUID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/not-a-valid-uuid-123`,
            });
            expect(result.statusCode).toEqual(400);
        });

        it('should fail with numeric id', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/123`,
            });
            expect(result.statusCode).toEqual(400);
        });
    });

    describe('GET /posts/owner/:id - 查询自己发布的文章详情', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/owner/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/posts/owner/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401); // 先检查认证，再检查UUID
        });
    });

    describe('POST /posts - 新增文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {
                    title: 'Test Post',
                    body: 'Test content',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/posts`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /posts - 更新自己发布的文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    title: 'Updated Post',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /posts - 批量删除自己发布的文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/posts`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                    trash: false,
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /posts/restore - 批量恢复自己发布的文章', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/posts/restore`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
