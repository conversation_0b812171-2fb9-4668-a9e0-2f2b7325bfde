import { describe } from 'node:test';

import { NestFastifyApplication } from '@nestjs/platform-fastify';

import { DataSource } from 'typeorm';

import { TagRepository } from '@/modules/content/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manage/content';

describe('Manager TagController test', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let tagRepository: TagRepository;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        tagRepository = app.get<TagRepository>(TagRepository);
        datasource = app.get<DataSource>(DataSource);
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }
    });

    it('check init', async () => {
        expect(app).toBeDefined();
        expect(tagRepository).toBeDefined();
    });

    describe('GET /tag - 分页查询标签列表', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid page parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?page=0`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid limit parameter without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag?limit=0`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('GET /tag/:id - 查询标签详情', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/74e655b3-b69a-42ae-a101-41c224386e74`,
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID format without authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/tag/invalid-uuid`,
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('POST /tag - 创建标签', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {
                    name: 'Test Tag',
                    desc: 'Test description',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {
                    name: 'A'.repeat(256),
                    desc: 'Test description',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long description without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {
                    name: 'Test Tag',
                    desc: 'A'.repeat(501),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {
                    name: '',
                    desc: 'Test description',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with whitespace name without authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/tag`,
                body: {
                    name: '   ',
                    desc: 'Test description',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('PATCH /tag - 更新标签', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/tag`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'Updated Tag',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty body without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/tag`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid id format without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/tag`,
                body: {
                    id: 'invalid-uuid',
                    name: 'Updated Tag',
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long name without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/tag`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'A'.repeat(256),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with long description without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/tag`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    desc: 'A'.repeat(501),
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with non-existent id without authentication', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/tag`,
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'Updated Tag',
                },
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    describe('DELETE /tag - 批量删除标签', () => {
        it('should fail without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/tag`,
                body: {
                    ids: ['74e655b3-b69a-42ae-a101-41c224386e74'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with empty ids array without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/tag`,
                body: {
                    ids: [],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail with invalid UUID in ids without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/tag`,
                body: {
                    ids: ['invalid-uuid'],
                },
            });
            expect(result.statusCode).toEqual(401);
        });

        it('should fail without ids field without authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/tag`,
                body: {},
            });
            expect(result.statusCode).toEqual(401);
        });
    });

    afterAll(async () => {
        await datasource.destroy();
        await app.close();
    });
});
