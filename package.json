{"name": "3r", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"cli": "bun --bun src/console/bin.ts", "dev": "cross-env NODE_ENV=development pnpm cli start -w", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "cross-env NODE_ENV=production nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env NODE_ENV=development nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@casl/ability": "^6.7.3", "@fastify/static": "^8.2.0", "@nestjs/common": "^11.1.3", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "bcrypt": "^6.0.0", "chalk": "^5.4.1", "chokidar": "^4.0.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.13", "deepmerge": "^4.3.1", "dotenv": "^16.5.0", "fastify": "^5.4.0", "find-up": "^7.0.0", "fs-extra": "^11.3.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "meilisearch": "^0.51.0", "mysql2": "^3.14.1", "ora": "^8.2.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pm2": "^6.0.8", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "sanitize-html": "^2.17.0", "typeorm": "^0.3.24", "uuid": "^11.1.0", "validator": "^13.15.15", "yaml": "^2.8.0", "yargs": "^18.0.0"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@faker-js/faker": "^9.8.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.1", "@types/bcrypt": "^5.0.2", "@types/eslint": "^9.6.1", "@types/fs-extra": "^11.0.4", "@types/jest": "29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.17", "@types/node": "^24.0.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/sanitize-html": "^2.16.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/validator": "^13.15.1", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "bun-types": "^1.2.16", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "jest": "30.0.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "~5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "testMatch": ["<rootDir>/test/**/*.test.ts"], "transform": {"^.+\\.(js|jsx)$": "ts-jest", "^.+\\.(ts|tsx)?$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "transformIgnorePatterns": ["node_modules/(?!(chalk))"], "testEnvironment": "node"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["webpack"]}}}