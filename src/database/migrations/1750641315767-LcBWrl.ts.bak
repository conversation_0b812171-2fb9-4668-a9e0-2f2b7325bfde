import typeorm = require('typeorm');

class LcBWrl1750641315767 implements typeorm.MigrationInterface {
    name = 'LcBWrl1750641315767'

    public async up(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`
        CREATE TABLE \`user_refresh_token\` (\`id\` varchar(36) NOT NULL, \`value\` varchar(500) NOT NULL COMMENT '令牌字符串', \`expiredAt\` datetime NOT NULL COMMENT '令牌过期时间', \`createdAt\` datetime(6) NOT NULL COMMENT '令牌创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`accessTokenId\` varchar(36) NULL, UNIQUE INDEX \`REL_0fb9e76570bb35fd7dd7f78f73\` (\`accessTokenId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        CREATE TABLE \`user_access_token\` (\`id\` varchar(36) NOT NULL, \`value\` varchar(500) NOT NULL COMMENT '令牌字符串', \`expiredAt\` datetime NOT NULL COMMENT '令牌过期时间', \`createdAt\` datetime(6) NOT NULL COMMENT '令牌创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`userId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        CREATE TABLE \`user\` (\`id\` varchar(36) NOT NULL, \`nickname\` varchar(64) NULL COMMENT '昵称', \`username\` varchar(64) NOT NULL COMMENT '用户名', \`password\` varchar(500) NOT NULL COMMENT '用户密码', \`phone\` varchar(64) NOT NULL COMMENT '用户手机号', \`email\` varchar(256) NULL COMMENT '用户邮箱', \`createdAt\` datetime(6) NOT NULL COMMENT '用户创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL COMMENT '用户更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL COMMENT '用户销户时间', UNIQUE INDEX \`IDX_78a916df40e02a9deb1c4b75ed\` (\`username\`), UNIQUE INDEX \`IDX_8e1f623798118e629b46a9e629\` (\`phone\`), UNIQUE INDEX \`IDX_e12875dfb3b1d92d7d7c5377e2\` (\`email\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` ADD \`authorId\` varchar(36) NOT NULL
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` ADD \`authorId\` varchar(36) NOT NULL
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` ADD CONSTRAINT \`FK_70b9cb9c33e723c66ea68715268\` FOREIGN KEY (\`authorId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` ADD CONSTRAINT \`FK_8fcc2d81ced7b8ade2bbd151b1a\` FOREIGN KEY (\`authorId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_refresh_token\` ADD CONSTRAINT \`FK_0fb9e76570bb35fd7dd7f78f73c\` FOREIGN KEY (\`accessTokenId\`) REFERENCES \`user_access_token\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_access_token\` ADD CONSTRAINT \`FK_c9c6ac4970ddbe5a8c4887e1e7e\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
    `);
    }

    public async down(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`
        ALTER TABLE \`user_access_token\` DROP FOREIGN KEY \`FK_c9c6ac4970ddbe5a8c4887e1e7e\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_refresh_token\` DROP FOREIGN KEY \`FK_0fb9e76570bb35fd7dd7f78f73c\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` DROP FOREIGN KEY \`FK_8fcc2d81ced7b8ade2bbd151b1a\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` DROP FOREIGN KEY \`FK_70b9cb9c33e723c66ea68715268\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` DROP COLUMN \`authorId\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` DROP COLUMN \`authorId\`
    `);
        await queryRunner.query(`
        DROP INDEX \`IDX_e12875dfb3b1d92d7d7c5377e2\` ON \`user\`
    `);
        await queryRunner.query(`
        DROP INDEX \`IDX_8e1f623798118e629b46a9e629\` ON \`user\`
    `);
        await queryRunner.query(`
        DROP INDEX \`IDX_78a916df40e02a9deb1c4b75ed\` ON \`user\`
    `);
        await queryRunner.query(`
        DROP TABLE \`user\`
    `);
        await queryRunner.query(`
        DROP TABLE \`user_access_token\`
    `);
        await queryRunner.query(`
        DROP INDEX \`REL_0fb9e76570bb35fd7dd7f78f73\` ON \`user_refresh_token\`
    `);
        await queryRunner.query(`
        DROP TABLE \`user_refresh_token\`
    `);
    }

}

module.exports = LcBWrl1750641315767
