/* eslint-disable */
export default async () => {
    const t = {
        ["./modules/content/entities/post.entity"]: await import("./modules/content/entities/post.entity"),
        ["./modules/content/entities/comment.entity"]: await import("./modules/content/entities/comment.entity"),
        ["./modules/user/entities/user.entity"]: await import("./modules/user/entities/user.entity"),
        ["./modules/content/constants"]: await import("./modules/content/constants"),
        ["./modules/content/entities/category.entity"]: await import("./modules/content/entities/category.entity"),
        ["./modules/content/entities/tag.entity"]: await import("./modules/content/entities/tag.entity"),
        ["./modules/user/entities/access.token.entity"]: await import("./modules/user/entities/access.token.entity"),
        ["./modules/user/entities/refresh.token.entity"]: await import("./modules/user/entities/refresh.token.entity"),
        ["./modules/rbac/entities/role.entity"]: await import("./modules/rbac/entities/role.entity"),
        ["./modules/database/constants"]: await import("./modules/database/constants"),
        ["./modules/user/constants"]: await import("./modules/user/constants")
    };
    return { "@nestjs/swagger": { "models": [[import("./modules/content/entities/comment.entity"), { "CommentEntity": { id: { required: true, type: () => String }, body: { required: true, type: () => String }, createdAt: { required: true, type: () => Date }, post: { required: true, type: () => t["./modules/content/entities/post.entity"].PostEntity }, depth: { required: true, type: () => Object, default: 0 }, parent: { required: true, type: () => t["./modules/content/entities/comment.entity"].CommentEntity, nullable: true }, children: { required: true, type: () => [t["./modules/content/entities/comment.entity"].CommentEntity] }, author: { required: true, type: () => t["./modules/user/entities/user.entity"].UserEntity } } }], [import("./modules/content/entities/tag.entity"), { "TagEntity": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, desc: { required: false, type: () => String }, postCount: { required: true, type: () => Number }, posts: { required: true, type: () => [t["./modules/content/entities/post.entity"].PostEntity] } } }], [import("./modules/content/entities/post.entity"), { "PostEntity": { id: { required: true, type: () => String }, title: { required: true, type: () => String }, body: { required: true, type: () => String }, summary: { required: false, type: () => String }, keywords: { required: false, type: () => [String] }, type: { required: true, enum: t["./modules/content/constants"].PostBodyType }, publishedAt: { required: false, type: () => Date, nullable: true }, customOrder: { required: true, type: () => Number }, createdAt: { required: false, type: () => Date }, updatedAt: { required: false, type: () => Date }, deleteAt: { required: true, type: () => Date }, commentCount: { required: true, type: () => Number }, category: { required: true, type: () => t["./modules/content/entities/category.entity"].CategoryEntity }, tags: { required: true, type: () => [t["./modules/content/entities/tag.entity"].TagEntity] }, comments: { required: true, type: () => [t["./modules/content/entities/comment.entity"].CommentEntity] }, author: { required: true, type: () => t["./modules/user/entities/user.entity"].UserEntity } } }], [import("./modules/content/entities/category.entity"), { "CategoryEntity": { id: { required: true, type: () => String }, name: { required: true, type: () => String }, customOrder: { required: true, type: () => Number }, posts: { required: true, type: () => [t["./modules/content/entities/post.entity"].PostEntity] }, depth: { required: true, type: () => Object, default: 0 }, parent: { required: true, type: () => t["./modules/content/entities/category.entity"].CategoryEntity, nullable: true }, children: { required: true, type: () => [t["./modules/content/entities/category.entity"].CategoryEntity] } } }], [import("./modules/user/entities/refresh.token.entity"), { "RefreshTokenEntity": { accessToken: { required: true, type: () => t["./modules/user/entities/access.token.entity"].AccessTokenEntity, description: "\u5173\u8054\u7684\u767B\u5F55\u4EE4\u724C" } } }], [import("./modules/user/entities/access.token.entity"), { "AccessTokenEntity": { refreshToken: { required: true, type: () => t["./modules/user/entities/refresh.token.entity"].RefreshTokenEntity, description: "\u5173\u8054\u7684\u5237\u65B0\u4EE4\u724C" }, user: { required: true, type: () => t["./modules/user/entities/user.entity"].UserEntity, description: "\u5173\u8054\u7528\u6237" } } }], [import("./modules/rbac/entities/role.entity"), { "RoleEntity": { id: { required: true, type: () => String, description: "\u89D2\u8272ID" }, name: { required: true, type: () => String, description: "\u89D2\u8272\u540D\u79F0" }, label: { required: false, type: () => String, description: "\u663E\u793A\u540D\u79F0" }, description: { required: false, type: () => String, description: "\u89D2\u8272\u63CF\u8FF0" }, systemed: { required: false, type: () => Boolean, description: "\u662F\u5426\u4E3A\u4E0D\u53EF\u66F4\u6539\u7684\u7CFB\u7EDF\u6743\u9650" }, deletedAt: { required: true, type: () => Date, description: "\u5220\u9664\u65F6\u95F4" }, permissions: { required: true, description: "\u89D2\u8272\u6743\u9650" }, users: { required: true, type: () => [t["./modules/user/entities/user.entity"].UserEntity], description: "\u89D2\u8272\u5173\u8054\u7528\u6237" } } }], [import("./modules/rbac/entities/permission.entity"), { "PermissionEntity": { id: { required: true, type: () => String, description: "\u6743\u9650ID" }, name: { required: true, type: () => String, description: "\u6743\u9650\u540D\u79F0" }, label: { required: false, type: () => String, description: "\u6743\u9650\u663E\u793A\u540D" }, description: { required: false, type: () => String, description: "\u6743\u9650\u63CF\u8FF0" }, rule: { required: true, type: () => Object, description: "\u6743\u9650\u89C4\u5219" }, roles: { required: true, type: () => [t["./modules/rbac/entities/role.entity"].RoleEntity], description: "\u6743\u9650\u89D2\u8272" }, users: { required: true, type: () => [t["./modules/user/entities/user.entity"].UserEntity], description: "\u6743\u9650\u7528\u6237" } } }], [import("./modules/user/entities/user.entity"), { "UserEntity": { id: { required: true, type: () => String, description: "\u7528\u6237ID" }, nickname: { required: false, type: () => String, description: "\u7528\u6237\u6635\u79F0" }, username: { required: true, type: () => String, description: "\u7528\u6237\u540D" }, password: { required: true, type: () => String, description: "\u7528\u6237\u5BC6\u7801" }, phone: { required: false, type: () => String, description: "\u7528\u6237\u624B\u673A\u53F7" }, email: { required: false, type: () => String, description: "\u7528\u6237\u90AE\u7BB1" }, createdAt: { required: false, type: () => Date, description: "\u7528\u6237\u521B\u5EFA\u65F6\u95F4" }, updatedAt: { required: false, type: () => Date, description: "\u7528\u6237\u66F4\u65B0\u65F6\u95F4" }, deletedAt: { required: false, type: () => Date, description: "\u7528\u6237\u9500\u6237\u65F6\u95F4" }, posts: { required: true, type: () => [t["./modules/content/entities/post.entity"].PostEntity], description: "\u7528\u6237\u53D1\u8868\u6587\u7AE0" }, comments: { required: true, type: () => [t["./modules/content/entities/comment.entity"].CommentEntity], description: "\u7528\u6237\u53D1\u8868\u8BC4\u8BBA" }, accessTokens: { required: true, type: () => [t["./modules/user/entities/access.token.entity"].AccessTokenEntity], description: "\u767B\u5F55token" }, permissions: { required: true, description: "\u7528\u6237\u6743\u9650" }, roles: { required: true, type: () => [t["./modules/rbac/entities/role.entity"].RoleEntity], description: "\u7528\u6237\u89D2\u8272" } } }], [import("./modules/user/dtos/user.common.dto"), { "UserCommonDto": { credential: { required: true, type: () => String, description: "\u767B\u5F55\u51ED\u8BC1:\u53EF\u4EE5\u662F\u7528\u6237\u540D,\u624B\u673A\u53F7,\u90AE\u7BB1\u5730\u5740", minLength: 4, maxLength: 30 }, username: { required: true, type: () => String, description: "\u7528\u6237\u540D", minLength: 4, maxLength: 50 }, nickname: { required: true, type: () => String, description: "\u6635\u79F0:\u4E0D\u8BBE\u7F6E\u5219\u4E3A\u7528\u6237\u540D", minLength: 3, maxLength: 20 }, phone: { required: true, type: () => String, description: "\u624B\u673A\u53F7:\u5FC5\u987B\u662F\u533A\u57DF\u5F00\u5934\u7684,\u6BD4\u5982+86.15005255555" }, email: { required: true, type: () => String, description: "\u90AE\u7BB1\u5730\u5740:\u5FC5\u987B\u7B26\u5408\u90AE\u7BB1\u5730\u5740\u89C4\u5219", format: "email" }, password: { required: true, type: () => String, description: "\u7528\u6237\u5BC6\u7801:\u5BC6\u7801\u5FC5\u987B\u7531\u5C0F\u5199\u5B57\u6BCD,\u5927\u5199\u5B57\u6BCD,\u6570\u5B57\u4EE5\u53CA\u7279\u6B8A\u5B57\u7B26\u7EC4\u6210", minLength: 8, maxLength: 50 }, plainPassword: { required: true, type: () => String, description: "\u786E\u8BA4\u5BC6\u7801:\u5FC5\u987B\u4E0E\u7528\u6237\u5BC6\u7801\u8F93\u5165\u76F8\u540C\u7684\u5B57\u7B26\u4E32" } } }], [import("./modules/user/dtos/auth.dto"), { "CredentialDto": {}, "RegisterDto": {} }], [import("./modules/restful/dtos/paginate.dto"), { "PaginateDto": { page: { required: false, type: () => Number, description: "\u5F53\u524D\u9875", default: 1, minimum: 1 }, limit: { required: false, type: () => Number, description: "\u6BCF\u9875\u6570\u636E\u91CF", default: 10, minimum: 1 } } }], [import("./modules/restful/dtos/paginate-width-trashed.dto"), { "PaginateWithTrashedDto": { trashed: { required: false, description: "\u6839\u636E\u8F6F\u5220\u9664\u72B6\u6001\u67E5\u8BE2", enum: t["./modules/database/constants"].SelectTrashMode } } }], [import("./modules/user/dtos/user.dto"), { "CreateUserDto": { roles: { required: false, type: () => [String], description: "\u7528\u6237\u5173\u8054\u7684\u89D2\u8272ID\u5217\u8868", format: "uuid" }, permissions: { required: false, type: () => [String], description: "\u7528\u6237\u76F4\u63A5\u5173\u8054\u7684\u6743\u9650ID\u5217\u8868", format: "uuid" } }, "UpdateUserDto": { id: { required: true, type: () => String, description: "\u5F85\u66F4\u65B0\u7684\u7528\u6237ID", format: "uuid" } }, "QueryUserDto": { role: { required: false, type: () => String, description: "\u89D2\u8272ID:\u6839\u636E\u89D2\u8272\u6765\u8FC7\u6EE4\u7528\u6237", format: "uuid" }, permission: { required: false, type: () => String, description: "\u6743\u9650ID:\u6839\u636E\u6743\u9650\u6765\u8FC7\u6EE4\u7528\u6237(\u6743\u9650\u5305\u542B\u7528\u6237\u5173\u8054\u7684\u6240\u6709\u89D2\u8272\u7684\u6743\u9650\u4EE5\u53CA\u76F4\u63A5\u5173\u8054\u7684\u6743\u9650)", format: "uuid" }, orderBy: { required: false, description: "\u6392\u5E8F\u89C4\u5219:\u53EF\u6307\u5B9A\u7528\u6237\u5217\u8868\u7684\u6392\u5E8F\u89C4\u5219,\u9ED8\u8BA4\u4E3A\u6309\u521B\u5EFA\u65F6\u95F4\u964D\u5E8F\u6392\u5E8F", enum: t["./modules/user/constants"].UserOrderType } }, "FrontedQueryUserDto": {} }], [import("./modules/user/dtos/account.dto"), { "UpdateAccountDto": { id: { required: true, type: () => String, description: "\u5F85\u66F4\u65B0\u7684\u7528\u6237ID", format: "uuid" } }, "UpdatePasswordDto": { id: { required: true, type: () => String, description: "\u5F85\u66F4\u65B0\u7684\u7528\u6237ID", format: "uuid" }, oldPassword: { required: true, type: () => String, description: "\u65E7\u5BC6\u7801:\u7528\u6237\u5728\u66F4\u6539\u5BC6\u7801\u65F6\u9700\u8981\u8F93\u5165\u7684\u539F\u5BC6\u7801", minLength: 8, maxLength: 50 } } }], [import("./modules/rbac/dtos/permission.dto"), { "QueryPermissionDto": { role: { required: false, type: () => String, description: "\u89D2\u8272ID:\u901A\u8FC7\u89D2\u8272\u8FC7\u6EE4\u6743\u9650\u5217\u8868", format: "uuid" } } }], [import("./modules/content/dtos/category.dto"), { "CreateCategoryDto": { name: { required: true, type: () => String, maxLength: 25 }, parent: { required: false, type: () => String, format: "uuid" }, customOrder: { required: false, type: () => Number, default: 0, minimum: 0 } }, "UpdateCategoryDto": { id: { required: true, type: () => String, format: "uuid" } } }], [import("./modules/content/dtos/tag.dto"), { "CreateTagDto": { name: { required: true, type: () => String, maxLength: 255 }, desc: { required: false, type: () => String, maxLength: 500 } }, "UpdateTagDto": { id: { required: true, type: () => String, format: "uuid" } } }], [import("./modules/content/dtos/comment.dto"), { "QueryCommentDto": { page: { required: true, type: () => Object, default: 1, minimum: 1 }, limit: { required: true, type: () => Object, default: 10, minimum: 1 }, post: { required: false, type: () => String, format: "uuid" } }, "QueryCommentTreeDto": {}, "CreateCommentDto": { body: { required: true, type: () => String, maxLength: 1000 }, post: { required: true, type: () => String, format: "uuid" }, parent: { required: false, type: () => String, format: "uuid" } }, "DeleteCommentDto": { ids: { required: true, type: () => [String], format: "uuid" } } }], [import("./modules/content/dtos/post.dto"), { "QueryPostDto": { isPublished: { required: false, type: () => Boolean, description: "\u662F\u5426\u67E5\u8BE2\u5DF2\u53D1\u5E03(\u5168\u90E8\u6587\u7AE0:\u4E0D\u586B\u3001\u53EA\u67E5\u8BE2\u5DF2\u53D1\u5E03\u7684:true\u3001\u53EA\u67E5\u8BE2\u672A\u53D1\u5E03\u7684:false)" }, search: { required: false, type: () => String, description: "\u5168\u6587\u641C\u7D22", maxLength: 100 }, orderBy: { required: false, description: "\u67E5\u8BE2\u7ED3\u679C\u6392\u5E8F,\u4E0D\u586B\u5219\u7EFC\u5408\u6392\u5E8F", enum: t["./modules/content/constants"].PostOrder }, page: { required: true, type: () => Object, default: 1, minimum: 1 }, limit: { required: true, type: () => Object, default: 10, minimum: 1 }, trashed: { required: false, enum: t["./modules/database/constants"].SelectTrashMode }, category: { required: false, type: () => String, description: "\u6839\u636E\u5206\u7C7BID\u67E5\u8BE2\u6B64\u5206\u7C7B\u53CA\u5176\u540E\u4EE3\u5206\u7C7B\u4E0B\u7684\u6587\u7AE0", format: "uuid" }, tag: { required: false, type: () => String, description: "\u6839\u636E\u6807\u7B7EID\u67E5\u8BE2", format: "uuid" }, author: { required: false, type: () => String, description: "\u6839\u636E\u6587\u7AE0\u4F5C\u8005ID\u67E5\u8BE2", format: "uuid" } }, "CreatePostDto": { title: { required: true, type: () => String, description: "\u6587\u7AE0\u6807\u9898", maxLength: 255 }, body: { required: true, type: () => String, description: "\u6587\u7AE0\u5185\u5BB9" }, summary: { required: false, type: () => String, description: "\u6587\u7AE0\u63CF\u8FF0", maxLength: 500 }, publish: { required: false, type: () => Boolean, description: "\u662F\u5426\u53D1\u5E03(\u53D1\u5E03\u65F6\u95F4)" }, keywords: { required: false, type: () => [String], description: "SEO\u5173\u952E\u5B57", maxLength: 20 }, customOrder: { required: false, type: () => Number, description: "\u81EA\u5B9A\u4E49\u6392\u5E8F", minimum: 0 }, category: { required: false, type: () => String, description: "\u6240\u5C5E\u5206\u7C7BID", format: "uuid" }, tags: { required: false, type: () => [String], description: "\u5173\u8054\u6807\u7B7EID", format: "uuid" }, author: { required: false, type: () => String, description: "\u6587\u7AE0\u4F5C\u8005ID:\u53EF\u7528\u4E8E\u5728\u7BA1\u7406\u5458\u53D1\u5E03\u6587\u7AE0\u65F6\u5206\u914D\u7ED9\u5176\u5B83\u7528\u6237,\u5982\u679C\u4E0D\u8BBE\u7F6E,\u5219\u4F5C\u8005\u4E3A\u5F53\u524D\u7BA1\u7406\u5458", format: "uuid" } }, "UpdatePostDto": { id: { required: true, type: () => String, description: "\u5F85\u66F4\u65B0ID", format: "uuid" } }, "FrontendQueryPostDto": {}, "FrontendCreatePostDto": { userOrder: { required: false, type: () => Number, description: "\u7528\u6237\u4FA7\u6392\u5E8F:\u6587\u7AE0\u5728\u7528\u6237\u7684\u6587\u7AE0\u7BA1\u7406\u800C\u975E\u540E\u53F0\u4E2D,\u5217\u8868\u7684\u6392\u5E8F\u89C4\u5219", default: 0, minimum: 0 } }, "OwnerUpdatePostDto": { userOrder: { required: false, type: () => Number, description: "\u7528\u6237\u4FA7\u6392\u5E8F:\u6587\u7AE0\u5728\u7528\u6237\u7684\u6587\u7AE0\u7BA1\u7406\u800C\u975E\u540E\u53F0\u4E2D,\u5217\u8868\u7684\u6392\u5E8F\u89C4\u5219", default: 0, minimum: 0 } }, "OwnerQueryPostDto": {} }], [import("./modules/content/dtos/delete.dto"), { "DeleteDto": { ids: { required: true, type: () => [String], format: "uuid" } } }], [import("./modules/content/dtos/delete.with.trash.dto"), { "DeleteWithTrashDto": { trash: { required: false, type: () => Boolean } }, "RestoreDto": { ids: { required: true, type: () => [String], format: "uuid" } } }]], "controllers": [[import("./modules/content/controllers/category.controller"), { "CategoryController": { "tree": { summary: "Search category tree", type: [t["./modules/content/entities/category.entity"].CategoryEntity] }, "list": { summary: "\u5206\u9875\u67E5\u8BE2\u5206\u7C7B\u5217\u8868" }, "detail": { summary: "\u67E5\u8BE2\u5206\u7C7B\u660E\u7EC6", type: t["./modules/content/entities/category.entity"].CategoryEntity } } }], [import("./modules/content/controllers/tag.controller"), { "TagController": { "list": { summary: "\u5206\u9875\u67E5\u8BE2\u6807\u7B7E\u5217\u8868" }, "detail": { summary: "\u67E5\u8BE2\u6807\u7B7E\u8BE6\u60C5", type: t["./modules/content/entities/tag.entity"].TagEntity } } }], [import("./modules/content/controllers/post.controller"), { "PostController": { "list": { summary: "\u67E5\u8BE2\u6587\u7AE0\u5217\u8868", type: Object }, "listOwner": { summary: "\u5206\u9875\u67E5\u8BE2\u81EA\u5DF1\u53D1\u5E03\u7684\u6587\u7AE0\u5217\u8868", type: Object }, "show": { summary: "\u67E5\u8BE2\u6587\u7AE0\u8BE6\u60C5", type: t["./modules/content/entities/post.entity"].PostEntity }, "detailOwner": { summary: "\u67E5\u8BE2\u81EA\u5DF1\u53D1\u5E03\u7684\u6587\u7AE0\u8BE6\u60C5", type: t["./modules/content/entities/post.entity"].PostEntity }, "store": { summary: "\u65B0\u589E\u6587\u7AE0", type: t["./modules/content/entities/post.entity"].PostEntity }, "update": { summary: "\u66F4\u65B0\u81EA\u5DF1\u53D1\u5E03\u7684\u6587\u7AE0", type: t["./modules/content/entities/post.entity"].PostEntity }, "delete": { summary: "\u6279\u91CF\u5220\u9664\u81EA\u5DF1\u53D1\u5E03\u7684\u6587\u7AE0", type: [t["./modules/content/entities/post.entity"].PostEntity] }, "restore": { summary: "\u6279\u91CF\u6062\u590D\u81EA\u5DF1\u53D1\u5E03\u7684\u6587\u7AE0", type: [t["./modules/content/entities/post.entity"].PostEntity] } } }], [import("./modules/content/controllers/comment.controller"), { "CommentController": { "tree": { summary: "\u67E5\u8BE2\u8BC4\u8BBA\u6811", type: [t["./modules/content/entities/comment.entity"].CommentEntity] }, "list": { summary: "\u67E5\u8BE2\u8BC4\u8BBA\u5217\u8868" }, "store": { summary: "\u65B0\u589E\u8BC4\u8BBA", type: t["./modules/content/entities/comment.entity"].CommentEntity }, "delete": { summary: "\u6279\u91CF\u5220\u9664\u8BC4\u8BBA", type: [t["./modules/content/entities/comment.entity"].CommentEntity] } } }], [import("./modules/content/controllers/manager/category.controller"), { "CategoryController": { "tree": { summary: "Search category tree", type: [t["./modules/content/entities/category.entity"].CategoryEntity] }, "list": { summary: "\u5206\u9875\u67E5\u8BE2\u5206\u7C7B\u5217\u8868" }, "detail": { summary: "\u67E5\u8BE2\u5206\u7C7B\u660E\u7EC6", type: t["./modules/content/entities/category.entity"].CategoryEntity }, "store": { type: t["./modules/content/entities/category.entity"].CategoryEntity }, "update": { type: t["./modules/content/entities/category.entity"].CategoryEntity }, "delete": { type: [t["./modules/content/entities/category.entity"].CategoryEntity] } } }], [import("./modules/content/controllers/manager/tag.controller"), { "TagController": { "list": { summary: "\u5206\u9875\u67E5\u8BE2\u6807\u7B7E\u5217\u8868" }, "detail": { summary: "\u67E5\u8BE2\u6807\u7B7E\u8BE6\u60C5", type: t["./modules/content/entities/tag.entity"].TagEntity }, "store": { summary: "\u6DFB\u52A0\u65B0\u6807\u7B7E", type: t["./modules/content/entities/tag.entity"].TagEntity }, "update": { summary: "\u66F4\u65B0\u6807\u7B7E", type: t["./modules/content/entities/tag.entity"].TagEntity }, "delete": { summary: "\u6279\u91CF\u5220\u9664\u6807\u7B7E", type: [t["./modules/content/entities/tag.entity"].TagEntity] } } }], [import("./modules/content/controllers/manager/post.controller"), { "PostController": { "manageList": { summary: "\u67E5\u8BE2\u6587\u7AE0\u5217\u8868", type: Object }, "manageDetail": { summary: "\u67E5\u8BE2\u6587\u7AE0\u8BE6\u60C5", type: t["./modules/content/entities/post.entity"].PostEntity }, "storeManage": { summary: "\u65B0\u589E\u6587\u7AE0", type: t["./modules/content/entities/post.entity"].PostEntity }, "manageUpdate": { summary: "\u66F4\u65B0\u6587\u7AE0", type: t["./modules/content/entities/post.entity"].PostEntity }, "manageDelete": { summary: "\u6279\u91CF\u5220\u9664\u6587\u7AE0", type: [t["./modules/content/entities/post.entity"].PostEntity] }, "manageRestore": { summary: "\u6279\u91CF\u6062\u590D\u6587\u7AE0", type: [t["./modules/content/entities/post.entity"].PostEntity] } } }], [import("./modules/content/controllers/manager/comment.controller"), { "CommentController": { "list": { summary: "\u67E5\u8BE2\u8BC4\u8BBA\u5217\u8868" }, "delete": { summary: "\u6279\u91CF\u5220\u9664\u8BC4\u8BBA", type: [t["./modules/content/entities/comment.entity"].CommentEntity] } } }], [import("./modules/rbac/controllers/role.controller"), { "RoleController": { "list": { summary: "\u89D2\u8272\u5217\u8868\u67E5\u8BE2" }, "detail": { summary: "\u89D2\u8272\u8BE6\u89E3\u67E5\u8BE2", type: t["./modules/rbac/entities/role.entity"].RoleEntity } } }], [import("./modules/rbac/controllers/manager/role.controller"), { "RoleController": { "store": { summary: "\u65B0\u589E\u89D2\u8272", type: t["./modules/rbac/entities/role.entity"].RoleEntity }, "update": { summary: "\u66F4\u65B0\u89D2\u8272", type: t["./modules/rbac/entities/role.entity"].RoleEntity }, "delete": { summary: "\u6279\u91CF\u5220\u9664\u89D2\u8272", type: [t["./modules/rbac/entities/role.entity"].RoleEntity] }, "restore": { summary: "\u6279\u91CF\u6062\u590D\u89D2\u8272", type: [t["./modules/rbac/entities/role.entity"].RoleEntity] } } }], [import("./modules/rbac/controllers/manager/permission.controller"), { "PermissionController": { "list": { summary: "\u5206\u9875\u5217\u8868\u67E5\u8BE2" }, "detail": { summary: "\u5206\u9875\u5217\u8868\u67E5\u8BE2" } } }], [import("./modules/user/controllers/account.controller"), { "AccountController": { "register": { summary: "\u4F7F\u7528\u7528\u6237\u540D\u5BC6\u7801\u6CE8\u518C\u7528\u6237", type: t["./modules/user/entities/user.entity"].UserEntity }, "login": { summary: "\u7528\u6237\u767B\u5F55[\u51ED\u8BC1(\u53EF\u4EE5\u662F\u7528\u6237\u540D,\u90AE\u7BB1,\u624B\u673A\u53F7\u7B49)+\u5BC6\u7801\u767B\u5F55]" }, "logout": { summary: "\u6CE8\u9500\u767B\u5F55" }, "profile": { summary: "\u83B7\u53D6\u8D26\u6237\u4FE1\u606F[\u53EA\u6709\u7528\u6237\u81EA\u5DF1\u624D\u80FD\u67E5\u8BE2]", type: t["./modules/user/entities/user.entity"].UserEntity }, "update": { summary: "\u66F4\u6539\u8D26\u6237\u4FE1\u606F", type: t["./modules/user/entities/user.entity"].UserEntity }, "changePassword": { summary: "\u4FEE\u6539\u5BC6\u7801[\u5FC5\u987B\u77E5\u9053\u539F\u5BC6\u7801]", type: t["./modules/user/entities/user.entity"].UserEntity } } }], [import("./modules/user/controllers/user.controller"), { "UserController": { "list": { summary: "\u7528\u6237\u5217\u8868", type: [t["./modules/user/entities/user.entity"].UserEntity] }, "detail": { summary: "\u83B7\u53D6\u7528\u6237\u4FE1\u606F", type: t["./modules/user/entities/user.entity"].UserEntity } } }], [import("./modules/user/controllers/manager/user.controller"), { "UserController": { "list": { summary: "\u7528\u6237\u5217\u8868", type: [t["./modules/user/entities/user.entity"].UserEntity] }, "detail": { summary: "\u83B7\u53D6\u7528\u6237\u4FE1\u606F", type: t["./modules/user/entities/user.entity"].UserEntity }, "store": { summary: "\u65B0\u589E\u7528\u6237", type: t["./modules/user/entities/user.entity"].UserEntity }, "update": { summary: "\u66F4\u65B0\u7528\u6237", type: t["./modules/user/entities/user.entity"].UserEntity }, "delete": { summary: "\u6279\u91CF\u5220\u9664\u7528\u6237", type: [t["./modules/user/entities/user.entity"].UserEntity] }, "restore": { summary: "\u6279\u91CF\u6062\u590D\u7528\u6237", type: [t["./modules/user/entities/user.entity"].UserEntity] } } }]] } };
};